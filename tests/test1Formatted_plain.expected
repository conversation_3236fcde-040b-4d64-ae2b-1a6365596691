my_string=	
my_string.to_string()="\t"
my_string=\
my_string.to_string()="\\"
my_string=foo
my_string.to_string()="foo"
my_int=9
my_int.to_string()=9
my_array=
	[0]=1
	[1]=2
	[2]=3
	[3]=null
	[4]=5
my_array.to_string()=[1,2,3,null,5]
my_array=
	[0]=3
	[1]=1
	[2]=2
	[3]=null
	[4]=0
my_array.to_string()=[3,1,2,null,0]
my_array=
	[0]=null
	[1]=0
	[2]=1
	[3]=2
	[4]=3
my_array.to_string()=[null,0,1,2,3]
baz_obj.to_string()="fark"
my_object=
	abc: 12
	foo: "bar"
	bool0: false
	bool1: true
my_object.to_string()={"abc":12,"foo":"bar","bool0":false,"bool1":true}
