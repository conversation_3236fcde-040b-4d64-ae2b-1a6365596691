==== delete-in-loop test starting ====
Key at index 0 is [foo1] (kept)
Key at index 1 is [foo2] (kept)
Key at index 2 is [deleteme] (deleted)
Key at index 3 is [foo3] (kept)
==== replace-value first loop starting ====
Key at index 0 is [foo1]
Key at index 1 is [foo2]
replacing value for key [foo2]
Key at index 2 is [foo3]
==== second loop starting ====
Key at index 0 is [foo1]
Key at index 1 is [foo2]
pointer for key [foo2] does match
Key at index 2 is [foo3]
