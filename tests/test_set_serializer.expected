Test setting, then resetting a custom serializer:
my_object.to_string(standard)={ "abc": 12, "foo": "bar" }
my_object.to_string(custom serializer)=Custom Output
Next line of output should be from the custom freeit function:
freeit, value=123
my_object.to_string(standard)={ "abc": 12, "foo": "bar" }
Check that the custom serializer isn't free'd until the last json_object_put:
my_object.to_string(custom serializer)=Custom Output
Next line of output should be from the custom freeit function:
freeit, value=123
