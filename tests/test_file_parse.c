#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <sys/stat.h>
#include "json.h"

/**
 * 读取文件内容到字符串
 */
char* read_file_content(const char* filename) {
    FILE *file;
    char *content;
    long file_size;
    size_t bytes_read;
    
    // 打开文件
    file = fopen(filename, "r");
    if (file == NULL) {
        fprintf(stderr, "Error: Cannot open file '%s': %s\n", filename, strerror(errno));
        return NULL;
    }
    
    // 获取文件大小
    if (fseek(file, 0, SEEK_END) != 0) {
        fprintf(stderr, "Error: Cannot seek to end of file '%s': %s\n", filename, strerror(errno));
        fclose(file);
        return NULL;
    }
    
    file_size = ftell(file);
    if (file_size == -1) {
        fprintf(stderr, "Error: Cannot get file size of '%s': %s\n", filename, strerror(errno));
        fclose(file);
        return NULL;
    }
    
    if (fseek(file, 0, SEEK_SET) != 0) {
        fprintf(stderr, "Error: Cannot seek to beginning of file '%s': %s\n", filename, strerror(errno));
        fclose(file);
        return NULL;
    }
    
    // 分配内存（+1 for null terminator）
    content = malloc(file_size + 1);
    if (content == NULL) {
        fprintf(stderr, "Error: Cannot allocate memory for file content: %s\n", strerror(errno));
        fclose(file);
        return NULL;
    }
    
    // 读取文件内容
    bytes_read = fread(content, 1, file_size, file);
    if (bytes_read != (size_t)file_size) {
        if (ferror(file)) {
            fprintf(stderr, "Error: Cannot read file '%s': %s\n", filename, strerror(errno));
        } else {
            fprintf(stderr, "Error: Incomplete read of file '%s'\n", filename);
        }
        free(content);
        fclose(file);
        return NULL;
    }
    
    // 添加null terminator
    content[file_size] = '\0';
    
    fclose(file);
    return content;
}

/**
 * 打印JSON对象的类型信息
 */
const char* get_json_type_name(enum json_type type) {
    switch (type) {
        case json_type_null:    return "null";
        case json_type_boolean: return "boolean";
        case json_type_double:  return "double";
        case json_type_int:     return "int";
        case json_type_object:  return "object";
        case json_type_array:   return "array";
        case json_type_string:  return "string";
        default:                return "unknown";
    }
}

/**
 * 递归打印JSON对象结构
 */
void print_json_structure(struct json_object *obj, int indent_level) {
    if (obj == NULL) {
        printf("%*snull\n", indent_level * 2, "");
        return;
    }
    
    enum json_type type = json_object_get_type(obj);
    const char* type_name = get_json_type_name(type);
    
    switch (type) {
        case json_type_null:
            printf("%*s[%s] null\n", indent_level * 2, "", type_name);
            break;
            
        case json_type_boolean:
            printf("%*s[%s] %s\n", indent_level * 2, "", type_name, 
                   json_object_get_boolean(obj) ? "true" : "false");
            break;
            
        case json_type_double:
            printf("%*s[%s] %f\n", indent_level * 2, "", type_name, 
                   json_object_get_double(obj));
            break;
            
        case json_type_int:
            printf("%*s[%s] %d\n", indent_level * 2, "", type_name, 
                   json_object_get_int(obj));
            break;
            
        case json_type_string:
            printf("%*s[%s] \"%s\"\n", indent_level * 2, "", type_name, 
                   json_object_get_string(obj));
            break;
            
        case json_type_array: {
            int array_len = json_object_array_length(obj);
            printf("%*s[%s] (length: %d)\n", indent_level * 2, "", type_name, array_len);
            for (int i = 0; i < array_len; i++) {
                printf("%*s[%d]:\n", (indent_level + 1) * 2, "", i);
                struct json_object *array_item = json_object_array_get_idx(obj, i);
                print_json_structure(array_item, indent_level + 2);
            }
            break;
        }
        
        case json_type_object: {
            printf("%*s[%s]\n", indent_level * 2, "", type_name);
            json_object_object_foreach(obj, key, val) {
                printf("%*s\"%s\":\n", (indent_level + 1) * 2, "", key);
                print_json_structure(val, indent_level + 2);
            }
            break;
        }
        
        default:
            printf("%*s[%s] <unknown type>\n", indent_level * 2, "", type_name);
            break;
    }
}

int main(int argc, char *argv[]) {
    if (argc != 2) {
        fprintf(stderr, "Usage: %s <json_file_path>\n", argv[0]);
        fprintf(stderr, "Example: %s test.json\n", argv[0]);
        return 1;
    }
    
    const char *filename = argv[1];
    
    printf("Reading JSON file: %s\n", filename);
    printf("=====================================\n");
    
    // 读取文件内容
    char *file_content = read_file_content(filename);
    if (file_content == NULL) {
        return 1;
    }
    
    printf("File content (%zu bytes):\n", strlen(file_content));
    printf("-------------------------------------\n");
    printf("%s\n", file_content);
    printf("-------------------------------------\n\n");
    
    // 使用json_tokener_parse解析JSON
    printf("Parsing JSON with json_tokener_parse()...\n");
    struct json_object *json_obj = json_tokener_parse(file_content);
    
    if (json_obj == NULL) {
        fprintf(stderr, "Error: Failed to parse JSON content\n");
        free(file_content);
        return 1;
    }
    
    printf("Parse successful!\n\n");
    
    // 打印解析结果
    printf("Parsed JSON structure:\n");
    printf("=====================================\n");
    print_json_structure(json_obj, 0);
    printf("\n");
    
    // 打印JSON字符串表示
    printf("JSON string representation:\n");
    printf("=====================================\n");
    const char *json_string = json_object_to_json_string(json_obj);
    printf("%s\n\n", json_string);
    
    // 打印格式化的JSON字符串
    printf("Pretty-printed JSON:\n");
    printf("=====================================\n");
    const char *json_string_pretty = json_object_to_json_string_ext(json_obj, JSON_C_TO_STRING_PRETTY);
    printf("%s\n", json_string_pretty);
    
    // 清理资源
    json_object_put(json_obj);
    free(file_content);
    
    printf("\nProgram completed successfully.\n");
    return 0;
}
