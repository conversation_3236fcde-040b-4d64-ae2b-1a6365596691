AC_PREREQ(2.52)

# Process this file with autoconf to produce a configure script.
AC_INIT([json-c], 0.11, [<EMAIL>])

AM_INIT_AUTOMAKE(AC_PACKAGE_NAME, AC_PACKAGE_VERSION)

AC_PROG_MAKE_SET

AC_ARG_ENABLE(oldname-compat,
 AS_HELP_STRING([--disable-oldname-compat],
   [Don't include the old libjson.so library and include/json directory.]),
[],
[enable_oldname_compat=yes]
)
AM_CONDITIONAL(ENABLE_OLDNAME_COMPAT, [test "x${enable_oldname_compat}" != "xno"])

# Checks for programs.

# Checks for libraries.

# Checks for header files.
AC_CONFIG_HEADER(config.h)
AC_CONFIG_HEADER(json_config.h)
AC_HEADER_STDC
AC_CHECK_HEADERS(fcntl.h limits.h strings.h syslog.h unistd.h [sys/cdefs.h] [sys/param.h] stdarg.h locale.h)
AC_CHECK_HEADER(inttypes.h,[AC_DEFINE([JSON_C_HAVE_INTTYPES_H],[1],[Public define for json_inttypes.h])])

# Checks for typedefs, structures, and compiler characteristics.
AC_C_CONST
AC_TYPE_SIZE_T

# Checks for library functions.
AC_FUNC_VPRINTF
AC_FUNC_MEMCMP
AC_FUNC_MALLOC
AC_FUNC_REALLOC
AC_CHECK_FUNCS(strcasecmp strdup strndup strerror snprintf vsnprintf vasprintf open vsyslog strncasecmp setlocale)

#check if .section.gnu.warning accepts long strings (for __warn_references)
AC_LANG_PUSH([C])

AC_MSG_CHECKING([if .gnu.warning accepts long strings])
AC_LINK_IFELSE([[
extern void json_object_get();
__asm__(".section .gnu.json_object_get,\n\t.ascii \"Please link against libjson-c instead of libjson\"\n\t.text");

int main(int c,char* v) {return 0;}
]], [
    AC_DEFINE(HAS_GNU_WARNING_LONG, 1, [Define if .gnu.warning accepts long strings.])
    AC_MSG_RESULT(yes)
], [
   AC_MSG_RESULT(no)
])

AC_LANG_POP([C])

AM_PROG_LIBTOOL

AC_CONFIG_FILES([
Makefile
json.pc
json-c.pc
tests/Makefile
json-c-uninstalled.pc
])

AC_OUTPUT

