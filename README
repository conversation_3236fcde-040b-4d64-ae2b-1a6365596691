Building on Unix with git, gcc and autotools

Home page for json-c:
  https://github.com/json-c/json-c/wiki

  Caution: do NOT use sources from svn.metaparadigm.com, they are old.

Prerequisites:
	gcc (or another C compiler)
	libtool

	If you're not using a release tarball, you'll also need:
	autoconf (autoreconf)
	automake

Github repo for json-c:
  https://github.com/json-c/json-c

    $ git clone https://github.com/json-c/json-c.git
    $ cd json-c
    $ sh autogen.sh

Then 

    $ ./configure
    $ make
    $ make install

To build and run the test programs run 

    $ make check

Linking to libjson-c

If your system has pkgconfig then you can just add this to your makefile

CFLAGS += $(shell pkg-config --cflags json-c)
LDFLAGS += $(shell pkg-config --libs json-c)

Without pkgconfig, you would do something like this:

JSON_C_DIR=/path/to/json_c/install
CFLAGS += -I$(JSON_C_DIR)/include/json-c
LDFLAGS+= -L$(JSON_C_DIR)/lib -ljson-c
